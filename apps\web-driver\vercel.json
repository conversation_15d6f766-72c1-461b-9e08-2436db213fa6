{"framework": "nextjs", "regions": ["sin1"], "functions": {"src/app/api/**/*.{js,ts,tsx}": {"maxDuration": 30}}, "build": {"env": {"NODE_OPTIONS": "--max-old-space-size=4096", "VERCEL": "1", "NEXT_TELEMETRY_DISABLED": "1", "SKIP_ENV_VALIDATION": "1"}}, "buildCommand": "cd ../.. && pnpm install && cd apps/web-driver && pnpm run build", "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}]}